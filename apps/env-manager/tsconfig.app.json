{"extends": "../../tsconfig.base.json", "compilerOptions": {"outDir": "dist", "lib": ["DOM", "DOM.Iterable", "ES2019"], "types": ["@react-router/node", "vite/client", "node"], "esModuleInterop": true, "jsx": "react-jsx", "resolveJsonModule": true, "allowJs": true, "forceConsistentCasingInFileNames": true, "rootDir": "app", "tsBuildInfoFile": "dist/tsconfig.app.tsbuildinfo"}, "include": ["app/**/*.ts", "app/**/*.tsx", "app/**/*.js", "app/**/*.jsx", "**/.server/**/*.ts", "**/.server/**/*.tsx", "**/.client/**/*.ts", "**/.client/**/*.tsx"], "exclude": ["out-tsc", "dist", "tests/**/*.spec.ts", "tests/**/*.test.ts", "tests/**/*.spec.tsx", "tests/**/*.test.tsx", "tests/**/*.spec.js", "tests/**/*.test.js", "tests/**/*.spec.jsx", "tests/**/*.test.jsx", "vite.config.ts", "vite.config.mts", "vitest.config.ts", "vitest.config.mts", "src/**/*.test.ts", "src/**/*.spec.ts", "src/**/*.test.tsx", "src/**/*.spec.tsx", "src/**/*.test.js", "src/**/*.spec.js", "src/**/*.test.jsx", "src/**/*.spec.jsx", "eslint.config.js", "eslint.config.cjs", "eslint.config.mjs"]}