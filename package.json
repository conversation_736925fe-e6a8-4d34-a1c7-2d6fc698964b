{"name": "@env-manager/source", "version": "0.0.0", "license": "MIT", "scripts": {}, "private": true, "dependencies": {"@react-router/node": "^7.2.0", "@react-router/serve": "^7.2.0", "isbot": "^4.4.0", "react": "19.0.0", "react-dom": "19.0.0", "react-router": "^7.2.0"}, "devDependencies": {"@eslint/js": "^9.8.0", "@nx/devkit": "21.3.11", "@nx/eslint": "21.3.11", "@nx/eslint-plugin": "21.3.11", "@nx/js": "21.3.11", "@nx/playwright": "21.3.11", "@nx/react": "21.3.11", "@nx/vite": "21.3.11", "@nx/web": "21.3.11", "@nx/workspace": "21.3.11", "@playwright/test": "^1.36.0", "@react-router/dev": "^7.2.0", "@swc-node/register": "~1.9.1", "@swc/cli": "~0.6.0", "@swc/core": "~1.5.7", "@swc/helpers": "~0.5.11", "@testing-library/dom": "10.4.0", "@testing-library/react": "16.1.0", "@types/node": "^20.0.0", "@types/react": "19.0.0", "@types/react-dom": "19.0.0", "@vitejs/plugin-react": "^4.2.0", "@vitest/coverage-v8": "^3.0.5", "@vitest/ui": "^3.0.0", "autoprefixer": "10.4.13", "eslint": "^9.8.0", "eslint-config-prettier": "^10.0.0", "eslint-plugin-import": "2.31.0", "eslint-plugin-jsx-a11y": "6.10.1", "eslint-plugin-playwright": "^1.6.2", "eslint-plugin-react": "7.35.0", "eslint-plugin-react-hooks": "5.0.0", "jiti": "2.4.2", "jsdom": "~22.1.0", "nx": "21.3.11", "postcss": "8.4.38", "prettier": "^2.6.2", "tailwindcss": "3.4.3", "tslib": "^2.3.0", "typescript": "~5.8.2", "typescript-eslint": "^8.29.0", "vite": "^6.0.0", "vitest": "^3.0.0"}, "packageManager": "pnpm@10.12.1+sha512.f0dda8580f0ee9481c5c79a1d927b9164f2c478e90992ad268bbb2465a736984391d6333d2c327913578b2804af33474ca554ba29c04a8b13060a717675ae3ac"}